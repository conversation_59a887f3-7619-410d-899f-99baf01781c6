lovebook-v2
============
the idea of the project is to create web app with 2 main options inside. the output of 2 service are real books. it should provide warm and love with its ux and ui. here are 2 main services.
1. quiz book - user can select receiver template (e.g. mother, father, friend, etc.) with prepared questions like (e.g. what is your warmest memory with your mother, how strong do you love your mother, what is the crasiest moment with your friend, etc.). then user can provide answers to this template questions and also edit questions or add new and answer them. also confgiure style and fonts and cover of the book.
2. story book - user starts new book, can type content manually, add images, configure style and fonts and cover of the book. also add audio files which are transcribed and text inserted to the book. so it more about story book which contains images and text.


first thing we need to start from is create landing page which can describe our platform. it should descrive 2 books ideas and have cta to dashboard. inside dashboard user will be able to choose which book to create. let's start from landing first.
